#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四元数驱动的机械臂3D打印系统演示
展示如何使用优化后的系统进行3D打印控制
"""

import sys
import os

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from improved_gcode_processor import execute_advanced_3d_printing


def main():
    """主演示函数"""
    print("=" * 80)
    print("🚀 四元数驱动的机械臂3D打印系统演示")
    print("=" * 80)
    print()
    print("本演示将展示以下优化特性:")
    print("  🧮 四元数姿态控制 - 解决万向节锁问题")
    print("  🎯 自适应轨迹优化 - 智能路径规划")
    print("  🔄 智能队列管理 - 平滑连续运动")
    print("  📊 实时质量监控 - 动态参数优化")
    print("  🔧 振动抑制系统 - 提升打印质量")
    print()
    print("⚠️  注意: 请确保机械臂已正确连接并处于安全状态")
    print()
    
    # 询问用户是否继续
    response = input("是否继续执行演示? (y/N): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("演示已取消")
        return
    
    print("\n🔄 启动优化系统...")
    
    try:
        # 执行优化的3D打印控制
        success = execute_advanced_3d_printing()
        
        if success:
            print("\n✅ 演示完成！系统运行正常")
            print("\n📊 主要改进:")
            print("  • 姿态控制更加平滑，避免了万向节锁")
            print("  • 自适应轨迹优化提升了运动质量")
            print("  • 智能队列管理实现了连续平滑运动")
            print("  • 实时振动抑制提升了打印精度")
        else:
            print("\n❌ 演示过程中出现问题，请检查系统状态")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
    
    print("\n" + "=" * 80)


if __name__ == "__main__":
    main()
