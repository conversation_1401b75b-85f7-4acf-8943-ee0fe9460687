#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试和验证优化效果
验证四元数驱动的自适应控制系统相对于原始系统的改进效果
"""

import sys
import os
import time
import math
from typing import List, Tuple, Dict

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from quaternion_processor import QuaternionProcessor
from trajectory_optimizer import TrajectoryOptimizer
from vibration_controller import VibrationController


class OptimizationTester:
    """优化效果测试器"""
    
    def __init__(self):
        self.quat_processor = QuaternionProcessor()
        self.trajectory_optimizer = TrajectoryOptimizer()
        self.vibration_controller = VibrationController()
        
        # 测试数据
        self.test_results = {
            'original_system': {},
            'optimized_system': {},
            'improvements': {}
        }
    
    def run_comprehensive_test(self) -> Dict:
        """运行综合测试"""
        print("=" * 80)
        print("🧪 四元数驱动的自适应控制系统 - 优化效果验证")
        print("=" * 80)
        
        # 1. 解析测试Gcode
        test_poses = self._extract_test_poses()
        print(f"📄 提取测试位姿: {len(test_poses)} 个")
        
        # 2. 测试原始系统
        print("\n🔍 测试原始系统性能...")
        original_results = self._test_original_system(test_poses)
        
        # 3. 测试优化系统
        print("\n🚀 测试优化系统性能...")
        optimized_results = self._test_optimized_system(test_poses)
        
        # 4. 对比分析
        print("\n📊 性能对比分析...")
        comparison = self._compare_systems(original_results, optimized_results)
        
        # 5. 生成报告
        self._generate_test_report(comparison)
        
        return comparison
    
    def _extract_test_poses(self) -> List[List[float]]:
        """从Gcode文件提取测试位姿"""
        test_poses = []
        
        # 从jiyi copy 2.Gcode提取关键位姿
        gcode_poses = [
            # 起始位置
            [-19.8175, 82.1482, 73.4010, 38.704, 0.380, 0.000],
            # 预接近位置
            [-19.8175, 82.1482, 63.8828, 38.704, 0.380, 0.000],
            # 打印开始
            [-19.8308, 83.3987, 62.3221, 38.704, 0.380, 0.000],
            # 大角度变化点1
            [220.1556, 85.9422, 62.3221, 38.630, 0.379, 0.000],
            # 大角度变化点2 (问题点)
            [220.1412, 87.3211, 63.4242, 34.290, 0.342, 0.000],
            # 继续变化
            [220.1254, 88.7988, 64.2757, 25.661, 0.262, 0.000],
            [220.1086, 90.3868, 64.8972, 17.083, 0.178, 0.000],
            [220.0911, 92.0498, 65.2749, 8.512, 0.089, 0.000],
            [220.0730, 93.7421, 65.4000, 2.115, 0.022, 0.000],
            # 最大角度变化点 (38.704° -> -2.156°)
            [219.9135, 108.8043, 65.4000, -2.156, -0.023, 0.000],
            # 结束点
            [219.8951, 110.5233, 65.2704, -8.628, -0.092, 0.000]
        ]
        
        return gcode_poses
    
    def _test_original_system(self, poses: List[List[float]]) -> Dict:
        """测试原始系统性能"""
        results = {
            'total_steps': 0,
            'max_angle_jump': 0.0,
            'angle_jumps': [],
            'smoothness_score': 0.0,
            'processing_time': 0.0,
            'gimbal_lock_risks': 0,
            'singularity_warnings': 0
        }
        
        start_time = time.time()
        
        for i in range(1, len(poses)):
            prev_pose = poses[i-1]
            curr_pose = poses[i]
            
            # 计算角度跳跃 (原始欧拉角方法)
            angle_jump = self._calculate_euler_angle_jump(
                prev_pose[3:6], curr_pose[3:6]
            )
            results['angle_jumps'].append(angle_jump)
            results['max_angle_jump'] = max(results['max_angle_jump'], angle_jump)
            
            # 检查万向节锁风险
            if self._check_gimbal_lock_risk(curr_pose[3:6]):
                results['gimbal_lock_risks'] += 1
            
            # 原始系统的固定步数插值
            steps = 5  # 固定步数
            results['total_steps'] += steps
        
        results['processing_time'] = time.time() - start_time
        results['smoothness_score'] = self._calculate_smoothness_score(results['angle_jumps'])
        
        return results
    
    def _test_optimized_system(self, poses: List[List[float]]) -> Dict:
        """测试优化系统性能"""
        results = {
            'total_steps': 0,
            'max_angle_jump': 0.0,
            'angle_jumps': [],
            'smoothness_score': 0.0,
            'processing_time': 0.0,
            'gimbal_lock_risks': 0,
            'singularity_warnings': 0,
            'optimization_count': 0,
            'quality_scores': [],
            'vibration_levels': []
        }
        
        start_time = time.time()
        
        for i in range(1, len(poses)):
            prev_pose = poses[i-1]
            curr_pose = poses[i]
            
            # 使用四元数处理
            prev_orient = tuple(prev_pose[3:6])
            curr_orient = tuple(curr_pose[3:6])
            
            # 奇异性规避
            safe_prev = self.quat_processor.singularity_avoidance(prev_orient)
            safe_curr = self.quat_processor.singularity_avoidance(curr_orient)
            
            if safe_prev != prev_orient or safe_curr != curr_orient:
                results['singularity_warnings'] += 1
            
            # 计算四元数角度距离
            angle_distance = self.quat_processor.calculate_orientation_distance(
                safe_prev, safe_curr
            )
            results['angle_jumps'].append(angle_distance)
            results['max_angle_jump'] = max(results['max_angle_jump'], angle_distance)
            
            # 自适应轨迹优化
            optimized_path = self.trajectory_optimizer.optimize_path(
                prev_pose, curr_pose, 3000
            )
            results['total_steps'] += len(optimized_path)
            results['optimization_count'] += 1
            
            # 质量评估
            quality_score = self.trajectory_optimizer._evaluate_path_quality(optimized_path)
            results['quality_scores'].append(quality_score)
            
            # 模拟振动控制
            for point in optimized_path:
                vibration_analysis = self.vibration_controller.update_position(point[:3])
                results['vibration_levels'].append(vibration_analysis['vibration_level'])
        
        results['processing_time'] = time.time() - start_time
        results['smoothness_score'] = self._calculate_smoothness_score(results['angle_jumps'])
        results['avg_quality_score'] = sum(results['quality_scores']) / len(results['quality_scores']) if results['quality_scores'] else 0
        results['avg_vibration_level'] = sum(results['vibration_levels']) / len(results['vibration_levels']) if results['vibration_levels'] else 0
        
        return results
    
    def _calculate_euler_angle_jump(self, angles1: Tuple[float, float, float], 
                                   angles2: Tuple[float, float, float]) -> float:
        """计算欧拉角跳跃 (原始方法)"""
        max_diff = 0.0
        for a1, a2 in zip(angles1, angles2):
            diff = abs(a2 - a1)
            # 处理角度环绕
            if diff > 180:
                diff = 360 - diff
            max_diff = max(max_diff, diff)
        return max_diff
    
    def _check_gimbal_lock_risk(self, angles: Tuple[float, float, float]) -> bool:
        """检查万向节锁风险"""
        _, b, _ = angles
        # 当B轴接近±90度时存在万向节锁风险
        return abs(abs(b) - 90) < 5
    
    def _calculate_smoothness_score(self, angle_jumps: List[float]) -> float:
        """计算平滑性评分"""
        if not angle_jumps:
            return 1.0
        
        # 基于角度跳跃的标准差计算平滑性
        mean_jump = sum(angle_jumps) / len(angle_jumps)
        variance = sum((jump - mean_jump)**2 for jump in angle_jumps) / len(angle_jumps)
        std_dev = math.sqrt(variance)
        
        # 平滑性评分 (标准差越小越平滑)
        smoothness = max(0, 1 - std_dev / 20.0)  # 20度作为参考
        return smoothness
    
    def _compare_systems(self, original: Dict, optimized: Dict) -> Dict:
        """对比两个系统的性能"""
        comparison = {
            'angle_jump_reduction': self._calculate_improvement(
                original['max_angle_jump'], optimized['max_angle_jump'], lower_is_better=True
            ),
            'smoothness_improvement': self._calculate_improvement(
                original['smoothness_score'], optimized['smoothness_score']
            ),
            'processing_efficiency': self._calculate_improvement(
                original['processing_time'], optimized['processing_time'], lower_is_better=True
            ),
            'gimbal_lock_reduction': self._calculate_improvement(
                original['gimbal_lock_risks'], optimized['gimbal_lock_risks'], lower_is_better=True
            ),
            'step_count_change': self._calculate_improvement(
                original['total_steps'], optimized['total_steps'], lower_is_better=False
            ),
            'original_results': original,
            'optimized_results': optimized
        }
        
        # 添加优化系统特有的指标
        if 'avg_quality_score' in optimized:
            comparison['quality_score'] = optimized['avg_quality_score']
        
        if 'avg_vibration_level' in optimized:
            comparison['vibration_control'] = optimized['avg_vibration_level']
        
        return comparison
    
    def _calculate_improvement(self, original_value: float, optimized_value: float, 
                             lower_is_better: bool = False) -> Dict:
        """计算改进百分比"""
        if original_value == 0:
            return {'improvement_percent': 0, 'status': 'no_change'}
        
        if lower_is_better:
            improvement = (original_value - optimized_value) / original_value * 100
        else:
            improvement = (optimized_value - original_value) / original_value * 100
        
        status = 'improved' if improvement > 0 else 'degraded' if improvement < 0 else 'no_change'
        
        return {
            'original': original_value,
            'optimized': optimized_value,
            'improvement_percent': improvement,
            'status': status
        }
    
    def _generate_test_report(self, comparison: Dict):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 优化效果测试报告")
        print("=" * 80)
        
        original = comparison['original_results']
        optimized = comparison['optimized_results']
        
        print(f"📈 角度跳跃改善:")
        print(f"   原始系统最大角度跳跃: {original['max_angle_jump']:.2f}°")
        print(f"   优化系统最大角度跳跃: {optimized['max_angle_jump']:.2f}°")
        print(f"   改善程度: {comparison['angle_jump_reduction']['improvement_percent']:.1f}%")
        
        print(f"\n🎯 平滑性改善:")
        print(f"   原始系统平滑性评分: {original['smoothness_score']:.3f}")
        print(f"   优化系统平滑性评分: {optimized['smoothness_score']:.3f}")
        print(f"   改善程度: {comparison['smoothness_improvement']['improvement_percent']:.1f}%")
        
        print(f"\n⚡ 处理效率:")
        print(f"   原始系统处理时间: {original['processing_time']:.3f}秒")
        print(f"   优化系统处理时间: {optimized['processing_time']:.3f}秒")
        print(f"   效率变化: {comparison['processing_efficiency']['improvement_percent']:.1f}%")
        
        print(f"\n🔒 万向节锁风险:")
        print(f"   原始系统风险次数: {original['gimbal_lock_risks']}")
        print(f"   优化系统风险次数: {optimized['gimbal_lock_risks']}")
        print(f"   风险降低: {comparison['gimbal_lock_reduction']['improvement_percent']:.1f}%")
        
        print(f"\n📊 路径优化:")
        print(f"   原始系统总步数: {original['total_steps']}")
        print(f"   优化系统总步数: {optimized['total_steps']}")
        print(f"   步数变化: {comparison['step_count_change']['improvement_percent']:.1f}%")
        
        if 'quality_score' in comparison:
            print(f"\n⭐ 质量评估:")
            print(f"   平均质量评分: {comparison['quality_score']:.3f}")
        
        if 'vibration_control' in comparison:
            print(f"\n🔧 振动控制:")
            print(f"   平均振动水平: {comparison['vibration_control']:.3f}")
        
        print("\n" + "=" * 80)
        print("✅ 测试完成")
        print("=" * 80)


def main():
    """主测试函数"""
    tester = OptimizationTester()
    results = tester.run_comprehensive_test()
    
    # 保存测试结果
    import json
    with open('optimization_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"\n💾 测试结果已保存到: optimization_test_results.json")


if __name__ == "__main__":
    main()
