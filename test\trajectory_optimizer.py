#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应轨迹优化模块
实现基于运动学约束的路径优化、自适应细分策略、运动学约束检查等功能
"""

import numpy as np
import math
from typing import List, Tuple, Dict, Optional
from quaternion_processor import QuaternionProcessor


class TrajectoryOptimizer:
    """轨迹优化器 - 基于运动学约束的智能路径规划"""
    
    def __init__(self, robot_limits: Optional[Dict] = None):
        """
        初始化轨迹优化器
        
        Args:
            robot_limits: 机械臂限位参数
        """
        self.quat_processor = QuaternionProcessor()
        
        # 默认机械臂限位 (度) - 可根据实际机械臂调整
        self.robot_limits = robot_limits or {
            'joint_limits': [
                (-170, 170),  # J1
                (-135, 135),  # J2
                (-135, 135),  # J3
                (-270, 270),  # J4
                (-135, 135),  # J5
                (-360, 360),  # J6
            ],
            'workspace': {
                'x_range': (-800, 800),    # mm
                'y_range': (-800, 800),    # mm
                'z_range': (0, 1200),      # mm
            }
        }
        
        # 轨迹优化参数
        self.position_threshold = 1.0      # mm - 位置细分阈值
        self.orientation_threshold = 5.0   # 度 - 姿态细分阈值
        self.max_velocity = 500.0          # mm/s - 最大线速度
        self.max_acceleration = 2000.0     # mm/s² - 最大加速度
        
        # 质量评估参数
        self.curvature_threshold = 0.1     # 曲率阈值
        self.smoothness_weight = 0.4       # 平滑性权重
        self.efficiency_weight = 0.6       # 效率权重
    
    def optimize_path(self, start: List[float], end: List[float], 
                     feedrate: float = 3000) -> List[List[float]]:
        """
        基于运动学约束的路径优化
        
        Args:
            start, end: [X, Y, Z, A, B, C] 起始和结束位姿
            feedrate: 进给速度 (mm/min)
            
        Returns:
            optimized_path: 优化后的路径点列表
        """
        # 分离位置和姿态
        start_pos, start_orient = start[:3], start[3:]
        end_pos, end_orient = end[:3], end[3:]
        
        # 检查工作空间约束
        if not self._check_workspace_constraints(start_pos) or \
           not self._check_workspace_constraints(end_pos):
            print("⚠️ 目标点超出工作空间范围")
            return [start, end]  # 返回原始路径
        
        # 奇异性规避
        start_orient = self.quat_processor.singularity_avoidance(tuple(start_orient))
        end_orient = self.quat_processor.singularity_avoidance(tuple(end_orient))
        
        # 自适应细分策略
        steps = self._calculate_adaptive_steps(start, end, feedrate)
        
        if steps <= 1:
            return [start, end]
        
        print(f"🔄 路径优化: 细分为 {steps} 步")
        
        # 位置线性插值
        positions = self._interpolate_positions(start_pos, end_pos, steps)
        
        # 姿态四元数插值
        orientations = self.quat_processor.interpolate_orientation(
            start_orient, end_orient, steps
        )
        
        # 合并路径点
        optimized_path = []
        for i in range(steps):
            point = list(positions[i]) + list(orientations[i])
            optimized_path.append(point)
        
        # 路径质量评估
        quality_score = self._evaluate_path_quality(optimized_path)
        print(f"📊 路径质量评分: {quality_score:.2f}/1.0")
        
        return optimized_path
    
    def _calculate_adaptive_steps(self, start: List[float], end: List[float], 
                                feedrate: float) -> int:
        """
        自适应计算细分步数
        
        Args:
            start, end: 起始和结束位姿
            feedrate: 进给速度 (mm/min)
            
        Returns:
            steps: 建议的细分步数
        """
        # 位置距离
        pos_delta = np.linalg.norm(np.array(end[:3]) - np.array(start[:3]))
        
        # 姿态距离
        orient_distance = self.quat_processor.calculate_orientation_distance(
            tuple(start[3:]), tuple(end[3:])
        )
        
        # 基于距离的步数计算
        pos_steps = max(1, int(pos_delta / self.position_threshold))
        orient_steps = max(1, int(orient_distance / self.orientation_threshold))
        
        # 基于速度的步数计算
        mm_per_sec = feedrate / 60.0
        time_duration = pos_delta / mm_per_sec if mm_per_sec > 0 else 1.0
        time_steps = max(1, int(time_duration * 10))  # 10Hz采样
        
        # 取最大值，确保足够的细分
        steps = max(pos_steps, orient_steps, time_steps)
        
        # 限制步数范围
        steps = max(1, min(steps, 50))
        
        print(f"📐 细分分析: 位置={pos_steps}, 姿态={orient_steps}, 时间={time_steps}, 最终={steps}")
        
        return steps
    
    def _interpolate_positions(self, start_pos: List[float], end_pos: List[float], 
                             steps: int) -> List[List[float]]:
        """
        位置线性插值
        
        Args:
            start_pos, end_pos: 起始和结束位置 [X, Y, Z]
            steps: 插值步数
            
        Returns:
            interpolated_positions: 插值后的位置序列
        """
        positions = []
        for i in range(1, steps + 1):
            t = i / steps
            pos = [
                start_pos[0] + t * (end_pos[0] - start_pos[0]),
                start_pos[1] + t * (end_pos[1] - start_pos[1]),
                start_pos[2] + t * (end_pos[2] - start_pos[2])
            ]
            positions.append(pos)
        
        return positions
    
    def _check_workspace_constraints(self, position: List[float]) -> bool:
        """
        检查位置是否在工作空间内
        
        Args:
            position: [X, Y, Z] 位置
            
        Returns:
            is_valid: 是否在工作空间内
        """
        x, y, z = position
        workspace = self.robot_limits['workspace']
        
        x_valid = workspace['x_range'][0] <= x <= workspace['x_range'][1]
        y_valid = workspace['y_range'][0] <= y <= workspace['y_range'][1]
        z_valid = workspace['z_range'][0] <= z <= workspace['z_range'][1]
        
        return x_valid and y_valid and z_valid
    
    def _check_kinematic_constraints(self, pose: List[float]) -> bool:
        """
        检查位姿是否满足运动学约束
        
        Args:
            pose: [X, Y, Z, A, B, C] 位姿
            
        Returns:
            is_reachable: 是否可达
        """
        # 简化的运动学检查 - 在实际应用中应使用真实的逆运动学求解器
        position = pose[:3]
        orientation = pose[3:]
        
        # 检查工作空间
        if not self._check_workspace_constraints(position):
            return False
        
        # 检查奇异性
        if self.quat_processor.detect_singularity(tuple(orientation)):
            print(f"⚠️ 检测到奇异性风险: {orientation}")
            return False
        
        # 简化的关节限位检查 (实际应用中需要完整的逆运动学)
        # 这里假设姿态角度直接对应某些关节角度
        for i, angle in enumerate(orientation):
            if i < len(self.robot_limits['joint_limits']):
                min_limit, max_limit = self.robot_limits['joint_limits'][i]
                if not (min_limit <= angle <= max_limit):
                    print(f"⚠️ 关节{i+4}角度超限: {angle}° (限制: {min_limit}°~{max_limit}°)")
                    return False
        
        return True
    
    def _evaluate_path_quality(self, path: List[List[float]]) -> float:
        """
        评估路径质量
        
        Args:
            path: 路径点列表
            
        Returns:
            quality_score: 质量评分 [0, 1]
        """
        if len(path) < 3:
            return 1.0
        
        # 计算平滑性指标
        smoothness_score = self._calculate_smoothness(path)
        
        # 计算效率指标 (路径长度vs直线距离)
        efficiency_score = self._calculate_efficiency(path)
        
        # 综合评分
        quality_score = (
            self.smoothness_weight * smoothness_score +
            self.efficiency_weight * efficiency_score
        )
        
        return quality_score
    
    def _calculate_smoothness(self, path: List[List[float]]) -> float:
        """
        计算路径平滑性
        
        Args:
            path: 路径点列表
            
        Returns:
            smoothness_score: 平滑性评分 [0, 1]
        """
        if len(path) < 3:
            return 1.0
        
        # 计算曲率变化
        curvature_changes = []
        for i in range(1, len(path) - 1):
            # 简化的曲率计算
            p1, p2, p3 = np.array(path[i-1][:3]), np.array(path[i][:3]), np.array(path[i+1][:3])
            
            v1 = p2 - p1
            v2 = p3 - p2
            
            if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                cos_angle = np.clip(cos_angle, -1, 1)
                angle_change = math.acos(cos_angle)
                curvature_changes.append(angle_change)
        
        if not curvature_changes:
            return 1.0
        
        # 平滑性评分 (曲率变化越小越平滑)
        avg_curvature = np.mean(curvature_changes)
        smoothness_score = max(0, 1 - avg_curvature / math.pi)
        
        return smoothness_score
    
    def _calculate_efficiency(self, path: List[List[float]]) -> float:
        """
        计算路径效率
        
        Args:
            path: 路径点列表
            
        Returns:
            efficiency_score: 效率评分 [0, 1]
        """
        if len(path) < 2:
            return 1.0
        
        # 计算实际路径长度
        actual_length = 0
        for i in range(1, len(path)):
            segment_length = np.linalg.norm(
                np.array(path[i][:3]) - np.array(path[i-1][:3])
            )
            actual_length += segment_length
        
        # 计算直线距离
        direct_length = np.linalg.norm(
            np.array(path[-1][:3]) - np.array(path[0][:3])
        )
        
        if direct_length == 0:
            return 1.0
        
        # 效率评分 (实际长度越接近直线距离越高效)
        efficiency_score = direct_length / actual_length
        
        return min(1.0, efficiency_score)
    
    def generate_velocity_profile(self, path: List[List[float]], 
                                feedrate: float) -> List[float]:
        """
        生成速度曲线
        
        Args:
            path: 路径点列表
            feedrate: 目标进给速度 (mm/min)
            
        Returns:
            velocity_profile: 每个路径点的速度 (mm/min)
        """
        if len(path) <= 1:
            return [feedrate]
        
        velocities = []
        target_velocity = feedrate
        
        for i in range(len(path)):
            # 在急转弯处降低速度
            if i > 0 and i < len(path) - 1:
                # 计算转角
                p1, p2, p3 = np.array(path[i-1][:3]), np.array(path[i][:3]), np.array(path[i+1][:3])
                v1 = p2 - p1
                v2 = p3 - p2
                
                if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
                    cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle = math.acos(cos_angle)
                    
                    # 根据转角调整速度
                    speed_factor = max(0.3, 1 - angle / math.pi)
                    velocity = target_velocity * speed_factor
                else:
                    velocity = target_velocity
            else:
                velocity = target_velocity
            
            velocities.append(velocity)
        
        return velocities
