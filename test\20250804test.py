# simple_test.py
import sys, os, time, math
sys.path.append(os.path.join(os.getcwd(), 'lib'))
import nrc_interface as nrc
from config import ROBOT_IP, ROBOT_PORT

def run_sanity_check():
    print("--- 启动最简可行性测试 ---")
    socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
    if socket_fd <= 0:
        print("❌ 连接失败"); return

    try:
        # 上电
        print("⚡ 正在上电...")
        # (这里直接使用您的上电函数逻辑)
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        nrc.set_servo_state(socket_fd, 1)
        time.sleep(0.5)
        if nrc.set_servo_poweron(socket_fd) != 0:
            print("❌ 上电失败"); return
        time.sleep(2)
        print("✅ 上电成功")

        # 获取当前位置
        pos_vec = nrc.VectorDouble()
        res = nrc.get_current_position(socket_fd, 0, pos_vec) # 0 = 关节坐标
        if res[0] != 0:
            print("❌ 无法获取当前位置"); return
        
        current_joints = [pos_vec[i] for i in range(6)]
        print(f"🔩 当前关节角度 (弧度): {[f'{q:.3f}' for q in current_joints]}")

        # 创建一个简单的关节运动指令
        print("🎯 准备执行一个微小的关节运动...")
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0
        move_cmd.coord = 0 # 关节坐标
        move_cmd.velocity = 10 # 10%速度
        move_cmd.acc = 10
        move_cmd.dec = 10
        move_cmd.pl = 0
        
        target_joints = current_joints.copy()
        target_joints[0] += math.radians(5) # 将第一个关节移动5度
        
        move_cmd.targetPosValue = nrc.VectorDouble()
        for j in target_joints:
            move_cmd.targetPosValue.append(j)
            
        # 使用直接指令模式（非队列）
        print("🚀 发送 robot_movej 指令...")
        if nrc.robot_movej(socket_fd, move_cmd) != 0:
            print("❌ movej 指令发送失败"); return
            
        # 等待运动完成
        print("⏳ 等待运动完成 (最多20秒)...")
        start_time = time.time()
        while time.time() - start_time < 20:
            status = 0
            res = nrc.get_robot_running_state(socket_fd, status)
            if res[1] == 0: # 0 = 停止
                print("✅ 运动完成！")
                break
            time.sleep(0.2)
        else:
            print("❌ 运动超时！")

    finally:
        print("🔌 正在下电和断开...")
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        nrc.disconnect_robot(socket_fd)
        print("✅ 测试结束")

if __name__ == "__main__":
    run_sanity_check()