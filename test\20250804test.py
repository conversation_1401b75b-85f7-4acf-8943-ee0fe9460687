#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G代码处理程序 (最终完整优化版)

功能特性：
1. 【架构优化】离线规划与在线执行完全分离，先计算后执行，稳定可靠。
2. 【核心优化】采用流式队列模式，维持缓冲区水位，实现真正平滑的连续运动。
3. 【逻辑修正】G0/G1指令按原始顺序处理，保证运动逻辑与G代码文件一致。
4. 【平滑处理】对G1指令的大角度变化进行线性位置+角度插值，消除姿态突变。
5. 【参数优化】提供关键参数（如pl平滑等级）的说明和建议值。
"""

import sys
import os
import time
import re
import math
from typing import List, Tuple, Dict, Union

# --- 请确保您的项目结构如下 ---
# your_project/
# ├── lib/
# │   ├── config.py
# │   └── nrc_interface.py
# └── src/
#     └── improved_gcode_processor_final.py (本文件)

# 添加lib目录到系统路径
# sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
# 为方便直接运行，我们假设lib在当前目录或Python路径中
sys.path.append(os.path.join(os.getcwd(), 'lib'))


# 假设您的config.py中有以下内容
# ROBOT_IP = "************"
# ROBOT_PORT = "6001"
try:
    from config import ROBOT_IP, ROBOT_PORT
except ImportError:
    print("错误：无法从lib/config.py导入ROBOT_IP和ROBOT_PORT。请创建该文件。")
    ROBOT_IP = "************" # 使用默认值
    ROBOT_PORT = "6001"

import nrc_interface as nrc

# === 配置参数 ===
# 请将GCODE_FILE指向您的G代码文件
GCODE_FILE = "jiyi copy 2.Gcode"
USER_COORD_NUMBER = 1

# 运动参数
G0_VELOCITY_PERCENT = 30
G1_VELOCITY_PERCENT = 20
ACCEL_PERCENT = 40
# 【关键参数】平滑等级(pl): 0=精确到达，拐角有停顿; 1-8=平滑过渡，数值越大拐角越圆滑。
# 对于3D打印，推荐从2或3开始测试。
SMOOTHING_LEVEL = 2 

# 角度配置（度）
G0_FIXED_ANGLES = (0.0, 0.0, 0.0)
G1_DEFAULT_ANGLES = (0.0, 0.0, 0.0)
ANGLE_OFFSET = (180.0, 0.0, 0.0)

# 流式处理参数
ANGLE_THRESHOLD = 20.0      # 角度变化超过此值(度)则进行插值平滑
SMOOTH_STEPS = 5            # 角度平滑的插值步数
QUEUE_BUFFER_MAX = 50       # 维持控制器队列中指令数量的目标上限
EXECUTION_TIMEOUT = 3600    # 总执行超时时间（秒），例如1小时

# === 辅助函数 ===
def normalize_angle(angle: float) -> float:
    while angle > 180: angle -= 360
    while angle <= -180: angle += 360
    return angle

def angle_difference(angle1: float, angle2: float) -> float:
    return normalize_angle(angle2 - angle1)

def max_angle_change(angles1: Tuple[float, float, float], angles2: Tuple[float, float, float]) -> float:
    return max(abs(angle_difference(a1, a2)) for a1, a2 in zip(angles1, angles2))

def needs_smoothing(angles1: Tuple[float, float, float], angles2: Tuple[float, float, float]) -> bool:
    return max_angle_change(angles1, angles2) > ANGLE_THRESHOLD

def generate_smooth_angles(start: Tuple[float, float, float], end: Tuple[float, float, float]) -> List[Tuple[float, float, float]]:
    transitions = []
    for i in range(1, SMOOTH_STEPS + 1):
        ratio = i / SMOOTH_STEPS
        interpolated = tuple(normalize_angle(s + angle_difference(s, e) * ratio) for s, e in zip(start, end))
        transitions.append(interpolated)
    return transitions

# === 核心处理器类 ===
class GCodeProcessor:
    """重构后的G代码处理器，分离规划与执行"""

    def __init__(self):
        self.socket_fd = -1
        
    def plan_trajectory_from_file(self, filepath: str) -> List[nrc.MoveCmd]:
        print("=" * 30)
        print("=== 阶段一：离线路径规划 ===")
        print("=" * 30)
        
        raw_instructions = self._parse_gcode_file(filepath)
        if not raw_instructions: return []

        planned_commands = []
        last_robot_angles = None
        last_position = None

        print("🔄 正在生成平滑的运动指令序列...")
        for instruction in raw_instructions:
            is_g0 = instruction['type'] == 'G0'
            velocity = G0_VELOCITY_PERCENT if is_g0 else G1_VELOCITY_PERCENT
            
            gcode_angles = self._get_instruction_angles(instruction, is_g0)
            current_robot_angles = self._convert_to_robot_angles(gcode_angles)
            current_position = (instruction['x'], instruction['y'], instruction['z'])

            if not is_g0 and last_robot_angles and last_position and needs_smoothing(last_robot_angles, current_robot_angles):
                print(f"  - 检测到G1角度突变，插入 {SMOOTH_STEPS} 个平滑过渡点...")
                smooth_transitions = generate_smooth_angles(last_robot_angles, current_robot_angles)
                
                for i, transition_angles in enumerate(smooth_transitions):
                    ratio = (i + 1) / SMOOTH_STEPS
                    interp_pos = tuple(lp + (cp - lp) * ratio for lp, cp in zip(last_position, current_position))
                    transition_cmd = self._create_move_command_from_pose(interp_pos, transition_angles, velocity)
                    planned_commands.append(transition_cmd)
            else:
                move_cmd = self._create_move_command(instruction, current_robot_angles, velocity)
                planned_commands.append(move_cmd)

            last_robot_angles = current_robot_angles
            last_position = current_position
        
        print(f"✅ 离线规划完成，总共生成 {len(planned_commands)} 个运动指令。")
        return planned_commands

    def stream_trajectory(self, planned_commands: List[nrc.MoveCmd]) -> bool:
        print("\n" + "=" * 30)
        print("=== 阶段二：在线流式执行 ===")
        print("=" * 30)

        if not planned_commands:
            print("ℹ️ 没有可执行的指令。")
            return True

        if not self._setup_queue_mode(): return False

        total_commands = len(planned_commands)
        cmd_idx = 0
        start_time = time.time()

        print("🚀 开始流式发送指令到机器人队列...")
        while cmd_idx < total_commands or self._get_queue_len() > 0:
            if time.time() - start_time > EXECUTION_TIMEOUT:
                print("\n❌ 执行超时！")
                nrc.queue_motion_stop(self.socket_fd)
                return False

            if not self._is_robot_healthy(): return False

            queue_len = self._get_queue_len()
            if queue_len == -1:
                print("\n⚠️ 无法获取队列长度，稍后重试...")
                time.sleep(0.5)
                continue
            
            while queue_len < QUEUE_BUFFER_MAX and cmd_idx < total_commands:
                result = nrc.queue_motion_push_back_moveL(self.socket_fd, planned_commands[cmd_idx])
                if result != 0:
                    print(f"\n❌ 添加指令到队列失败，错误码: {result}")
                    return False
                cmd_idx += 1
                queue_len += 1

            progress = (cmd_idx / total_commands) * 100 if total_commands > 0 else 100
            print(f"  [进度: {progress:6.2f}%] [已发送: {cmd_idx}/{total_commands}] [控制器队列: {queue_len:3d}]", end='\r')

            if cmd_idx == total_commands and self._get_queue_len() == 0:
                # 额外检查确保机器人已停止
                time.sleep(0.5) # 等待最后一个指令完成
                if self._get_robot_running_state() == 0:
                    break
            
            time.sleep(0.1)

        print("\n✅ 所有指令已执行完毕！")
        return True
    
    def _parse_gcode_file(self, filepath: str) -> List[Dict]:
        print(f"📄 正在解析G代码文件: {filepath}")
        instructions = []
        coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip().upper()
                    if not line or line.startswith(';'): continue
                    if line.startswith('G0') or line.startswith('G1'):
                        coords = dict(coord_regex.findall(line))
                        if 'X' in coords and 'Y' in coords and 'Z' in coords:
                            instructions.append({
                                'type': 'G0' if line.startswith('G0') else 'G1',
                                'x': float(coords['X']), 'y': float(coords['Y']), 'z': float(coords['Z']),
                                'a': float(coords.get('A')) if 'A' in coords else None,
                                'b': float(coords.get('B')) if 'B' in coords else None,
                                'c': float(coords.get('C')) if 'C' in coords else None
                            })
            print(f"✅ 解析完成: 共 {len(instructions)} 条有效G0/G1指令。")
            return instructions
        except FileNotFoundError:
            print(f"❌ 错误: G代码文件未找到 '{filepath}'")
            return []
        except Exception as e:
            print(f"❌ 解析G代码文件时发生错误: {e}")
            return []

    def _get_instruction_angles(self, instruction: dict, is_g0: bool) -> Tuple[float, float, float]:
        if is_g0: return G0_FIXED_ANGLES
        return (
            instruction['a'] if instruction['a'] is not None else G1_DEFAULT_ANGLES[0],
            instruction['b'] if instruction['b'] is not None else G1_DEFAULT_ANGLES[1],
            instruction['c'] if instruction['c'] is not None else G1_DEFAULT_ANGLES[2]
        )

    def _convert_to_robot_angles(self, gcode_angles: Tuple[float, float, float]) -> Tuple[float, float, float]:
        return tuple(normalize_angle(g + o) for g, o in zip(gcode_angles, ANGLE_OFFSET))
    
    def _create_move_command(self, instruction: dict, angles_deg: Tuple[float, float, float], velocity_percent: int) -> nrc.MoveCmd:
        return self._create_move_command_from_pose(
            (instruction['x'], instruction['y'], instruction['z']), angles_deg, velocity_percent
        )

    def _create_move_command_from_pose(self, position: Tuple[float, float, float], angles_deg: Tuple[float, float, float], velocity_percent: int) -> nrc.MoveCmd:
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0
        move_cmd.targetPosValue = nrc.VectorDouble()
        for pos in position: move_cmd.targetPosValue.append(pos)
        for angle in angles_deg: move_cmd.targetPosValue.append(math.radians(angle))
        move_cmd.coord = 3
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = velocity_percent
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL
        return move_cmd

    def _get_queue_len(self) -> int:
        q_len = 0
        res = nrc.queue_motion_get_queuelen(self.socket_fd, q_len)
        return res[1] if isinstance(res, list) and len(res) > 1 else -1

    def _get_robot_running_state(self) -> int:
        status = 0
        res = nrc.get_robot_running_state(self.socket_fd, status)
        return res[1] if isinstance(res, list) and len(res) > 1 else -1
        
    def _is_robot_healthy(self) -> bool:
        servo_status = 0
        res = nrc.get_servo_state(self.socket_fd, servo_status)
        if not (isinstance(res, list) and len(res) > 1):
            print("\n❌ 无法获取伺服状态！")
            return False
        status_code = res[1]
        if status_code == 2: # 2 = SERVO_ALARM
            print("\n❌ 机器人伺服报警！执行中止！")
            return False
        return True

    def _setup_queue_mode(self) -> bool:
        print("🔧 正在设置队列模式...")
        try:
            nrc.set_current_mode(self.socket_fd, 1)
            time.sleep(0.5)
            nrc.set_current_mode(self.socket_fd, 2)
            time.sleep(0.1)
            nrc.set_speed(self.socket_fd, 100)
            time.sleep(0.1)
            nrc.set_current_mode(self.socket_fd, 1)
            time.sleep(0.1)
            print("✅ 运行模式全局速度已预设为100%")

            if nrc.queue_motion_set_status(self.socket_fd, True) != 0:
                print("❌ 启动队列模式失败"); return False
            print("✅ 队列模式已启动")

            if nrc.queue_motion_clear_Data(self.socket_fd) != 0:
                print("❌ 清除队列数据失败"); return False
            print("✅ 队列数据已清除")
            return True
        except Exception as e:
            print(f"❌ 设置队列模式时发生错误: {e}"); return False

    def cleanup_queue_mode(self):
        print("🧹 正在清理队列模式...")
        try:
            # 停止可能仍在运行的队列运动
            nrc.queue_motion_stop(self.socket_fd)
            time.sleep(0.5)
            # 关闭队列模式
            if nrc.queue_motion_set_status(self.socket_fd, False) == 0:
                print("✅ 队列模式已关闭")
            else: print("⚠️ 关闭队列模式失败")
            # 切换回示教模式
            if nrc.set_current_mode(self.socket_fd, 0) == 0:
                print("✅ 已切换回示教模式")
            else: print("⚠️ 设置示教模式失败")
        except Exception as e:
            print(f"⚠️ 清理队列模式时发生错误: {e}")

# === 机器人控制辅助函数 ===
def robot_power_on_if_needed(socket_fd: int) -> bool:
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        current_status = result[1] if isinstance(result, list) and len(result) > 1 else -1

        if current_status == 3: # 3 = SERVO_RUNNING
            print("✅ 机器人伺服已上电。")
            return True

        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        
        # 如果是停止状态，先置为就绪
        if current_status == 0: # 0 = SERVO_STOP
            if nrc.set_servo_state(socket_fd, 1) != 0: # 1 = SERVO_READY
                print("❌ 设置伺服就绪状态失败！"); return False
            time.sleep(0.5)

        if nrc.set_servo_poweron(socket_fd) != 0:
            print(f"❌ 上电失败！请检查安全回路、急停按钮等。"); return False
        time.sleep(2)

        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}"); return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}"); return False

def robot_power_off(socket_fd: int) -> bool:
    print("\nℹ️ 正在安全下电...")
    try:
        if nrc.set_servo_poweroff(socket_fd) == 0:
            time.sleep(1)
            print("✅ 机器人已下电。")
            return True
        else:
            print("⚠️ 下电指令发送失败。")
            return False
    except Exception as e:
        print(f"❌ 下电过程失败: {e}"); return False

# === 主执行函数 ===
def execute_gcode_final():
    print("=" * 80)
    print("INEXBOT机械臂 G代码处理程序 (最终完整优化版)")
    print("=" * 80)

    processor = GCodeProcessor()

    try:
        planned_commands = processor.plan_trajectory_from_file(GCODE_FILE)
        if not planned_commands:
            print("❌ 路径规划失败或无有效指令，程序终止。")
            return

        print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        processor.socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if processor.socket_fd <= 0:
            print("❌ 连接失败！"); return
        print(f"✅ 连接成功！Socket ID: {processor.socket_fd}")

        if not robot_power_on_if_needed(processor.socket_fd): return

        print(f"\nℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        if nrc.set_user_coord_number(processor.socket_fd, USER_COORD_NUMBER) != 0:
            print("❌ 设置用户坐标系失败"); return
        time.sleep(0.2)

        processor.stream_trajectory(planned_commands)

        print("\n" + "=" * 80)
        print("🎉 G代码处理完成！")
        print("=" * 80)

    except KeyboardInterrupt:
        print("\n🛑 用户手动中断程序...")
    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        if processor.socket_fd > 0:
            print("\n--- 开始清理流程 ---")
            processor.cleanup_queue_mode()
            robot_power_off(processor.socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(processor.socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_final()