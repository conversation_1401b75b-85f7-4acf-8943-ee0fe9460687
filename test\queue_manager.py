#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
队列执行管理器
实现智能队列管理、实时状态监控、动态缓冲控制等功能
"""

import sys
import os
import time
import threading
import math
from typing import List, Dict, Optional, Callable
from queue import Queue, Empty

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc


class QueueManager:
    """智能队列管理器 - 实现平滑连续的机械臂运动控制"""
    
    def __init__(self, socket_fd: int):
        """
        初始化队列管理器
        
        Args:
            socket_fd: 机械臂连接套接字
        """
        self.socket_fd = socket_fd
        
        # 队列参数
        self.max_queue_size = 200      # 最大队列长度
        self.min_buffer_size = 30      # 最小缓冲区大小
        self.optimal_buffer_size = 100 # 最优缓冲区大小
        self.batch_size = 20           # 批次大小
        
        # 状态管理
        self.execution_active = False
        self.monitor_thread = None
        self.current_pose = None
        self.total_commands = 0
        self.executed_commands = 0
        
        # 性能监控
        self.performance_stats = {
            'queue_underruns': 0,
            'queue_overruns': 0,
            'avg_queue_length': 0,
            'execution_time': 0,
            'start_time': None
        }
        
        # 回调函数
        self.status_callback = None
        self.error_callback = None
        
        # 内部队列 (用于预处理)
        self.command_queue = Queue()
        self.processing_thread = None
        
    def initialize(self) -> bool:
        """
        初始化队列系统
        
        Returns:
            success: 初始化是否成功
        """
        try:
            print("🔧 正在初始化队列系统...")
            
            # 1. 设置远程模式
            result = nrc.set_current_mode(self.socket_fd, 1)  # 远程模式
            if result != 0:
                print(f"❌ 设置远程模式失败: {result}")
                return False
            
            # 2. 设置运行模式并配置全局速度
            result = nrc.set_current_mode(self.socket_fd, 2)  # 运行模式
            if result != 0:
                print(f"❌ 设置运行模式失败: {result}")
                return False
            
            # 3. 设置全局速度
            result = nrc.set_speed(self.socket_fd, 100)  # 100%全局速度
            if result != 0:
                print(f"❌ 设置全局速度失败: {result}")
                return False
            
            # 4. 启用队列模式
            result = nrc.queue_motion_set_status(self.socket_fd, True)
            if result != 0:
                print(f"❌ 启用队列模式失败: {result}")
                return False
            
            # 5. 清空队列
            result = nrc.queue_motion_clear_Data(self.socket_fd)
            if result != 0:
                print(f"❌ 清空队列失败: {result}")
                return False
            
            # 6. 启动监控线程
            self.execution_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_execution, daemon=True)
            self.monitor_thread.start()
            
            # 7. 启动命令处理线程
            self.processing_thread = threading.Thread(target=self._process_commands, daemon=True)
            self.processing_thread.start()
            
            self.performance_stats['start_time'] = time.time()
            print("✅ 队列系统初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ 队列系统初始化失败: {e}")
            return False
    
    def add_move_command(self, position: List[float], angles: List[float], 
                        velocity: int, coord_type: int = 3, user_num: int = 1) -> bool:
        """
        添加运动命令到队列
        
        Args:
            position: [X, Y, Z] 位置 (mm)
            angles: [A, B, C] 姿态角度 (度)
            velocity: 速度百分比
            coord_type: 坐标系类型 (3=用户坐标)
            user_num: 用户坐标系编号
            
        Returns:
            success: 是否成功添加
        """
        try:
            # 创建运动命令
            move_cmd = self._create_move_command(position, angles, velocity, coord_type, user_num)
            
            # 添加到内部队列进行预处理
            self.command_queue.put(move_cmd)
            self.total_commands += 1
            
            return True
            
        except Exception as e:
            print(f"❌ 添加运动命令失败: {e}")
            if self.error_callback:
                self.error_callback(f"添加命令失败: {e}")
            return False
    
    def add_path_batch(self, path_points: List[List[float]], feedrate: float) -> bool:
        """
        批量添加路径点
        
        Args:
            path_points: 路径点列表 [[X,Y,Z,A,B,C], ...]
            feedrate: 进给速度 (mm/min)
            
        Returns:
            success: 是否成功添加
        """
        velocity_percent = self._feedrate_to_velocity(feedrate)
        
        success_count = 0
        for point in path_points:
            if len(point) >= 6:
                position = point[:3]
                angles = point[3:6]
                
                if self.add_move_command(position, angles, velocity_percent):
                    success_count += 1
                else:
                    break
        
        print(f"📦 批量添加: {success_count}/{len(path_points)} 个路径点")
        return success_count == len(path_points)
    
    def _create_move_command(self, position: List[float], angles: List[float], 
                           velocity: int, coord_type: int, user_num: int) -> nrc.MoveCmd:
        """
        创建MoveCmd对象
        
        Args:
            position: [X, Y, Z] 位置
            angles: [A, B, C] 角度 (度)
            velocity: 速度百分比
            coord_type: 坐标系类型
            user_num: 用户坐标系编号
            
        Returns:
            move_cmd: 运动命令对象
        """
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0  # 实际坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        
        # 添加位置坐标
        for pos in position:
            move_cmd.targetPosValue.append(pos)
        
        # 添加姿态角度 (转换为弧度)
        for angle_deg in angles:
            move_cmd.targetPosValue.append(math.radians(angle_deg))
        
        # 设置运动参数
        move_cmd.coord = coord_type
        move_cmd.velocity = velocity
        move_cmd.acc = 80    # 加速度80%
        move_cmd.dec = 80    # 减速度80%
        move_cmd.pl = 3      # 平滑等级3
        move_cmd.toolNum = 1 # 工具号
        move_cmd.userNum = user_num
        
        return move_cmd
    
    def _process_commands(self):
        """
        命令处理线程 - 将内部队列的命令推送到机械臂队列
        """
        print("🔄 命令处理线程启动")
        
        while self.execution_active:
            try:
                # 检查机械臂队列状态
                current_queue_len = self._get_queue_length()
                
                # 如果队列有空间，添加更多命令
                if current_queue_len < self.optimal_buffer_size:
                    commands_to_add = min(
                        self.batch_size,
                        self.optimal_buffer_size - current_queue_len
                    )
                    
                    added_count = 0
                    for _ in range(commands_to_add):
                        try:
                            # 从内部队列获取命令 (非阻塞)
                            move_cmd = self.command_queue.get_nowait()
                            
                            # 推送到机械臂队列
                            result = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
                            if result == 0:
                                added_count += 1
                                self.executed_commands += 1
                            else:
                                print(f"⚠️ 推送命令失败: {result}")
                                break
                                
                        except Empty:
                            # 内部队列为空
                            break
                    
                    if added_count > 0:
                        print(f"📤 推送 {added_count} 个命令到机械臂队列")
                
                # 更新性能统计
                self._update_performance_stats(current_queue_len)
                
                time.sleep(0.05)  # 50ms循环
                
            except Exception as e:
                print(f"⚠️ 命令处理线程错误: {e}")
                time.sleep(0.1)
    
    def _monitor_execution(self):
        """
        执行监控线程 - 监控队列状态和机械臂运行状态
        """
        print("👁️ 执行监控线程启动")
        
        while self.execution_active:
            try:
                # 获取机械臂运行状态
                running_status = self._get_robot_running_state()
                queue_length = self._get_queue_length()
                
                # 检查队列下溢
                if queue_length < self.min_buffer_size and not self.command_queue.empty():
                    self.performance_stats['queue_underruns'] += 1
                    print(f"⚠️ 队列下溢警告: 当前长度 {queue_length}")
                
                # 检查队列上溢
                if queue_length > self.max_queue_size * 0.9:
                    self.performance_stats['queue_overruns'] += 1
                    print(f"⚠️ 队列接近满载: 当前长度 {queue_length}")
                
                # 状态回调
                if self.status_callback:
                    status_info = {
                        'queue_length': queue_length,
                        'running_status': running_status,
                        'progress': self._calculate_progress(),
                        'internal_queue_size': self.command_queue.qsize()
                    }
                    self.status_callback(status_info)
                
                time.sleep(0.1)  # 100ms监控周期
                
            except Exception as e:
                print(f"⚠️ 监控线程错误: {e}")
                time.sleep(0.5)
    
    def _get_queue_length(self) -> int:
        """获取当前队列长度"""
        try:
            queue_len = 0
            result = nrc.queue_motion_get_queuelen(self.socket_fd, queue_len)
            if isinstance(result, list) and len(result) > 1:
                return result[1]
            return 0
        except:
            return 0
    
    def _get_robot_running_state(self) -> int:
        """获取机械臂运行状态"""
        try:
            running_status = 0
            result = nrc.get_robot_running_state(self.socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                return result[1]
            return 0
        except:
            return 0
    
    def _feedrate_to_velocity(self, feedrate: float) -> int:
        """
        将Gcode进给速度转换为机械臂速度百分比
        
        Args:
            feedrate: 进给速度 (mm/min)
            
        Returns:
            velocity_percent: 速度百分比 [1, 100]
        """
        # 假设机械臂最大速度为500mm/s = 30000mm/min
        max_feedrate = 30000.0
        velocity_percent = int((feedrate / max_feedrate) * 100)
        return max(1, min(100, velocity_percent))
    
    def _calculate_progress(self) -> float:
        """计算执行进度"""
        if self.total_commands == 0:
            return 0.0
        return min(1.0, self.executed_commands / self.total_commands)
    
    def _update_performance_stats(self, queue_length: int):
        """更新性能统计"""
        # 更新平均队列长度
        if hasattr(self, '_queue_length_samples'):
            self._queue_length_samples.append(queue_length)
            if len(self._queue_length_samples) > 100:
                self._queue_length_samples.pop(0)
        else:
            self._queue_length_samples = [queue_length]
        
        self.performance_stats['avg_queue_length'] = sum(self._queue_length_samples) / len(self._queue_length_samples)
    
    def wait_for_completion(self, timeout: float = 300.0) -> bool:
        """
        等待队列执行完成
        
        Args:
            timeout: 超时时间 (秒)
            
        Returns:
            completed: 是否成功完成
        """
        print("⏳ 等待队列执行完成...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            queue_length = self._get_queue_length()
            internal_queue_size = self.command_queue.qsize()
            running_status = self._get_robot_running_state()
            
            # 检查是否完成
            if queue_length == 0 and internal_queue_size == 0 and running_status == 0:
                print("✅ 队列执行完成")
                return True
            
            # 显示进度
            progress = self._calculate_progress()
            print(f"\r📊 进度: {progress*100:.1f}% | 队列: {queue_length} | 内部: {internal_queue_size}", end="")
            
            time.sleep(0.5)
        
        print(f"\n❌ 等待超时 ({timeout}秒)")
        return False
    
    def stop(self):
        """停止队列执行"""
        print("🛑 正在停止队列执行...")
        
        try:
            # 停止队列运动
            nrc.queue_motion_stop(self.socket_fd)
            
            # 停止线程
            self.execution_active = False
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
            
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=2.0)
            
            # 关闭队列模式
            nrc.queue_motion_set_status(self.socket_fd, False)
            
            print("✅ 队列执行已停止")
            
        except Exception as e:
            print(f"⚠️ 停止队列时发生错误: {e}")
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        if self.performance_stats['start_time']:
            self.performance_stats['execution_time'] = time.time() - self.performance_stats['start_time']
        
        return self.performance_stats.copy()
    
    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback
    
    def set_error_callback(self, callback: Callable):
        """设置错误回调函数"""
        self.error_callback = callback
