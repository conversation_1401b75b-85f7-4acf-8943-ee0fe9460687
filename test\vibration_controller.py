#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动抑制和质量评估模块
实现实时振动控制、打印质量评估矩阵、参数自适应调整等高级功能
"""

import numpy as np
import time
import math
from typing import List, Dict, Tuple, Optional
from collections import deque


class VibrationController:
    """振动抑制控制器 - 实时监控和抑制机械臂振动"""
    
    def __init__(self, sampling_rate: float = 100.0):
        """
        初始化振动控制器
        
        Args:
            sampling_rate: 采样频率 (Hz)
        """
        self.sampling_rate = sampling_rate
        self.dt = 1.0 / sampling_rate
        
        # 滤波器参数
        self.cutoff_frequency = 15.0  # 截止频率 (Hz)
        self.damping_ratio = 0.65     # 阻尼比
        
        # 状态缓冲区
        self.position_buffer = deque(maxlen=int(sampling_rate * 2))  # 2秒缓冲
        self.velocity_buffer = deque(maxlen=int(sampling_rate * 2))
        self.acceleration_buffer = deque(maxlen=int(sampling_rate * 2))
        
        # 振动检测参数
        self.vibration_threshold = 0.5    # 振动检测阈值
        self.vibration_level = 0.0        # 当前振动水平
        self.vibration_history = deque(maxlen=100)
        
        # 自适应参数
        self.adaptive_enabled = True
        self.base_smoothing_level = 3
        self.current_smoothing_level = 3
        
        # 低通滤波器状态
        self._filter_state = None
        self._init_filter()
    
    def _init_filter(self):
        """初始化低通滤波器"""
        # 二阶Butterworth低通滤波器
        omega = 2 * math.pi * self.cutoff_frequency
        omega_d = omega * math.sqrt(1 - self.damping_ratio**2)
        
        # 离散化参数
        alpha = math.exp(-self.damping_ratio * omega * self.dt)
        beta = omega_d * self.dt
        
        self.filter_coeffs = {
            'a1': 2 * alpha * math.cos(beta),
            'a2': -alpha**2,
            'b0': (1 - alpha**2) / (1 + alpha**2),
            'b1': 0,
            'b2': -(1 - alpha**2) / (1 + alpha**2)
        }
        
        # 滤波器状态 [x(n-1), x(n-2), y(n-1), y(n-2)]
        self._filter_state = [0.0, 0.0, 0.0, 0.0]
    
    def update_position(self, position: List[float], timestamp: Optional[float] = None) -> Dict:
        """
        更新位置信息并进行振动分析
        
        Args:
            position: [X, Y, Z] 当前位置
            timestamp: 时间戳 (如果为None则使用当前时间)
            
        Returns:
            analysis_result: 振动分析结果
        """
        if timestamp is None:
            timestamp = time.time()
        
        # 添加到缓冲区
        self.position_buffer.append((timestamp, position))
        
        # 计算速度和加速度
        velocity = self._calculate_velocity()
        acceleration = self._calculate_acceleration()
        
        if velocity is not None:
            self.velocity_buffer.append((timestamp, velocity))
        
        if acceleration is not None:
            self.acceleration_buffer.append((timestamp, acceleration))
        
        # 振动检测
        vibration_metrics = self._detect_vibration()
        
        # 自适应参数调整
        if self.adaptive_enabled:
            self._adaptive_parameter_adjustment(vibration_metrics)
        
        return {
            'vibration_level': self.vibration_level,
            'smoothing_level': self.current_smoothing_level,
            'velocity_magnitude': np.linalg.norm(velocity) if velocity is not None else 0,
            'acceleration_magnitude': np.linalg.norm(acceleration) if acceleration is not None else 0,
            'filter_recommendation': self._get_filter_recommendation(),
            'quality_score': self._calculate_quality_score(vibration_metrics)
        }
    
    def _calculate_velocity(self) -> Optional[List[float]]:
        """计算当前速度"""
        if len(self.position_buffer) < 2:
            return None
        
        # 使用最近两个点计算速度
        t2, pos2 = self.position_buffer[-1]
        t1, pos1 = self.position_buffer[-2]
        
        dt = t2 - t1
        if dt <= 0:
            return None
        
        velocity = [(pos2[i] - pos1[i]) / dt for i in range(3)]
        return velocity
    
    def _calculate_acceleration(self) -> Optional[List[float]]:
        """计算当前加速度"""
        if len(self.velocity_buffer) < 2:
            return None
        
        # 使用最近两个速度点计算加速度
        t2, vel2 = self.velocity_buffer[-1]
        t1, vel1 = self.velocity_buffer[-2]
        
        dt = t2 - t1
        if dt <= 0:
            return None
        
        acceleration = [(vel2[i] - vel1[i]) / dt for i in range(3)]
        return acceleration
    
    def _detect_vibration(self) -> Dict:
        """检测振动水平"""
        if len(self.acceleration_buffer) < 10:
            return {'vibration_level': 0.0, 'frequency_content': {}}
        
        # 提取加速度数据
        accelerations = [acc for _, acc in list(self.acceleration_buffer)[-50:]]
        
        # 计算RMS加速度
        rms_acc = []
        for i in range(3):  # X, Y, Z轴
            axis_acc = [acc[i] for acc in accelerations]
            rms = math.sqrt(sum(a**2 for a in axis_acc) / len(axis_acc))
            rms_acc.append(rms)
        
        # 总振动水平
        total_vibration = math.sqrt(sum(rms**2 for rms in rms_acc))
        
        # 应用低通滤波
        filtered_vibration = self._apply_filter(total_vibration)
        
        self.vibration_level = filtered_vibration
        self.vibration_history.append(filtered_vibration)
        
        # 频率分析 (简化版)
        frequency_content = self._analyze_frequency_content(accelerations)
        
        return {
            'vibration_level': filtered_vibration,
            'rms_acceleration': rms_acc,
            'frequency_content': frequency_content,
            'vibration_trend': self._calculate_vibration_trend()
        }
    
    def _apply_filter(self, input_value: float) -> float:
        """应用低通滤波器"""
        if self._filter_state is None:
            return input_value
        
        # 二阶IIR滤波器
        coeffs = self.filter_coeffs
        state = self._filter_state
        
        # y(n) = b0*x(n) + b1*x(n-1) + b2*x(n-2) + a1*y(n-1) + a2*y(n-2)
        output = (coeffs['b0'] * input_value + 
                 coeffs['b1'] * state[0] + 
                 coeffs['b2'] * state[1] + 
                 coeffs['a1'] * state[2] + 
                 coeffs['a2'] * state[3])
        
        # 更新状态
        state[1] = state[0]  # x(n-2) = x(n-1)
        state[0] = input_value  # x(n-1) = x(n)
        state[3] = state[2]  # y(n-2) = y(n-1)
        state[2] = output  # y(n-1) = y(n)
        
        return output
    
    def _analyze_frequency_content(self, accelerations: List[List[float]]) -> Dict:
        """分析频率内容 (简化版FFT)"""
        if len(accelerations) < 16:
            return {}
        
        try:
            # 使用numpy进行简单的频率分析
            acc_magnitude = [math.sqrt(sum(acc[i]**2 for i in range(3))) for acc in accelerations]
            
            # 简化的频率分析
            fft_result = np.fft.fft(acc_magnitude)
            frequencies = np.fft.fftfreq(len(acc_magnitude), self.dt)
            
            # 找到主要频率成分
            magnitude = np.abs(fft_result)
            dominant_freq_idx = np.argmax(magnitude[1:len(magnitude)//2]) + 1
            dominant_frequency = abs(frequencies[dominant_freq_idx])
            
            return {
                'dominant_frequency': dominant_frequency,
                'magnitude': magnitude[dominant_freq_idx],
                'frequency_range': 'low' if dominant_frequency < 5 else 'medium' if dominant_frequency < 20 else 'high'
            }
            
        except Exception:
            return {}
    
    def _calculate_vibration_trend(self) -> str:
        """计算振动趋势"""
        if len(self.vibration_history) < 10:
            return 'stable'
        
        recent = list(self.vibration_history)[-10:]
        older = list(self.vibration_history)[-20:-10] if len(self.vibration_history) >= 20 else recent
        
        recent_avg = sum(recent) / len(recent)
        older_avg = sum(older) / len(older)
        
        if recent_avg > older_avg * 1.2:
            return 'increasing'
        elif recent_avg < older_avg * 0.8:
            return 'decreasing'
        else:
            return 'stable'
    
    def _adaptive_parameter_adjustment(self, vibration_metrics: Dict):
        """自适应参数调整"""
        vibration_level = vibration_metrics.get('vibration_level', 0)
        trend = vibration_metrics.get('vibration_trend', 'stable')
        
        # 根据振动水平调整平滑等级
        if vibration_level > self.vibration_threshold * 1.5:
            # 高振动 - 增加平滑等级
            self.current_smoothing_level = min(5, self.base_smoothing_level + 2)
        elif vibration_level > self.vibration_threshold:
            # 中等振动 - 适度增加平滑等级
            self.current_smoothing_level = min(4, self.base_smoothing_level + 1)
        elif vibration_level < self.vibration_threshold * 0.5:
            # 低振动 - 可以降低平滑等级提高效率
            self.current_smoothing_level = max(1, self.base_smoothing_level - 1)
        else:
            # 正常振动 - 使用基础平滑等级
            self.current_smoothing_level = self.base_smoothing_level
        
        # 根据趋势进行微调
        if trend == 'increasing':
            self.current_smoothing_level = min(5, self.current_smoothing_level + 1)
        elif trend == 'decreasing' and vibration_level < self.vibration_threshold:
            self.current_smoothing_level = max(1, self.current_smoothing_level - 1)
    
    def _get_filter_recommendation(self) -> Dict:
        """获取滤波器建议"""
        return {
            'cutoff_frequency': self.cutoff_frequency,
            'damping_ratio': self.damping_ratio,
            'smoothing_level': self.current_smoothing_level,
            'adaptive_enabled': self.adaptive_enabled
        }
    
    def _calculate_quality_score(self, vibration_metrics: Dict) -> float:
        """计算打印质量评分"""
        vibration_level = vibration_metrics.get('vibration_level', 0)
        trend = vibration_metrics.get('vibration_trend', 'stable')
        
        # 基础质量评分 (基于振动水平)
        base_score = max(0, 1 - vibration_level / (self.vibration_threshold * 2))
        
        # 趋势调整
        trend_factor = {
            'decreasing': 1.1,
            'stable': 1.0,
            'increasing': 0.9
        }.get(trend, 1.0)
        
        # 频率内容调整
        freq_content = vibration_metrics.get('frequency_content', {})
        freq_range = freq_content.get('frequency_range', 'medium')
        freq_factor = {
            'low': 1.0,      # 低频振动影响较小
            'medium': 0.9,   # 中频振动有一定影响
            'high': 0.8      # 高频振动影响较大
        }.get(freq_range, 0.9)
        
        # 综合质量评分
        quality_score = base_score * trend_factor * freq_factor
        
        return max(0, min(1, quality_score))
    
    def get_current_status(self) -> Dict:
        """获取当前状态"""
        return {
            'vibration_level': self.vibration_level,
            'smoothing_level': self.current_smoothing_level,
            'adaptive_enabled': self.adaptive_enabled,
            'buffer_sizes': {
                'position': len(self.position_buffer),
                'velocity': len(self.velocity_buffer),
                'acceleration': len(self.acceleration_buffer)
            },
            'vibration_trend': self._calculate_vibration_trend() if self.vibration_history else 'unknown'
        }
    
    def reset(self):
        """重置控制器状态"""
        self.position_buffer.clear()
        self.velocity_buffer.clear()
        self.acceleration_buffer.clear()
        self.vibration_history.clear()
        self.vibration_level = 0.0
        self.current_smoothing_level = self.base_smoothing_level
        self._init_filter()
    
    def set_parameters(self, cutoff_freq: Optional[float] = None, 
                      damping_ratio: Optional[float] = None,
                      vibration_threshold: Optional[float] = None):
        """设置控制器参数"""
        if cutoff_freq is not None:
            self.cutoff_frequency = cutoff_freq
            self._init_filter()
        
        if damping_ratio is not None:
            self.damping_ratio = damping_ratio
            self._init_filter()
        
        if vibration_threshold is not None:
            self.vibration_threshold = vibration_threshold
