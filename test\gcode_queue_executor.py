#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (队列模式版本) - 已修复v3

功能:
1. 读取一个标准的G-code文件。
2. 解析其中的G1移动指令 (包括X, Y, Z位置和A, B, C角度)。
3. 连接到INEXBOT机械臂。
4. 使用队列模式批量发送运动指令，实现更平滑的连续运动。
5. 在指定的用户坐标系下，将G-code路径点转换为机械臂的运动指令并执行。
6. 任务结束后安全下电并断开连接。

变更 v3:
- 【核心修复】修正了 MoveCmd 中 targetPosType 的值，应为 0 (PosType_data)，而不是 1。
- 修正了 MoveCmd 中 coord 的值，应为 1 (笛卡尔坐标)，同时指定 userNum。
- 在最终清理阶段使用正确的 queue_motion_stop 函数。
"""

import sys
import os
import time
import re
import math
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
# 假设 nrc_interface.py 与本文件在同一目录，或者在Python路径中
try:
    from config import ROBOT_IP, ROBOT_PORT
    import nrc_interface as nrc
except ImportError:
    print("❌ 错误: 无法导入 nrc_interface 或 config。")
    print("   请确保 nrc_interface.py 和 config.py 文件与此脚本位于同一目录，")
    print("   或者在Python系统路径中。")
    sys.exit(1)


def normalize_angle_degrees(angle):
    """
    将角度标准化到 [-180, 180] 范围内
    这与机械臂内部的角度标准化保持一致
    """
    while angle > 180:
        angle -= 360
    while angle <= -180:
        angle += 360
    return angle


def get_current_pose_angles(socket_fd):
    """获取机械臂当前姿态角度（度）"""
    try:
        pos = nrc.VectorDouble()
        # 用户坐标系为3，获取用户坐标系下的笛卡尔坐标
        result = nrc.get_current_position(socket_fd, 3, pos) 
        if result == 0 and len(pos) >= 6:
            # 将弧度转换为度并标准化
            rx_deg = normalize_angle_degrees(math.degrees(pos[3]))
            ry_deg = normalize_angle_degrees(math.degrees(pos[4]))
            rz_deg = normalize_angle_degrees(math.degrees(pos[5]))
            return [rx_deg, ry_deg, rz_deg]
        else:
            print(f"❌ 获取当前姿态失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前姿态时发生错误: {e}")
        return None

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 50  # 速度百分比 (0-100)
ACCEL_PERCENT = 20     # 加速度百分比 (0-100)
SMOOTHING_LEVEL = 0    # 平滑度 (0-8, 0表示精确定位)

# 4. 队列模式参数
QUEUE_BATCH_SIZE = 50
QUEUE_TIMEOUT = 300    # 队列执行超时时间 (秒)

# 5. G-code默认角度值
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

# 6. G-code角度到机械臂角度的偏移量
GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = 0.0

# 7. 计算出的机械臂默认姿态
DEFAULT_RX = GCODE_DEFAULT_A + GCODE_TO_ROBOT_OFFSET_A
DEFAULT_RY = GCODE_DEFAULT_B + GCODE_TO_ROBOT_OFFSET_B
DEFAULT_RZ = GCODE_DEFAULT_C + GCODE_TO_ROBOT_OFFSET_C


def parse_gcode_file(filepath):
    """解析G-code文件"""
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip().upper()
                if not line or line.startswith(';'):
                    continue
                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0)),
                            'a': float(coords.get('A')) if 'A' in coords else None,
                            'b': float(coords.get('B')) if 'B' in coords else None,
                            'c': float(coords.get('C')) if 'C' in coords else None
                        }
                        path.append(point)
        print(f"✅ 解析完成，共找到 {len(path)} 个路径点。")
        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None


def wait_for_queue_complete(socket_fd, timeout_seconds=300):
    """等待队列中所有运动完成"""
    print("  ⏳ 正在等待队列运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = time.time()

    while time.time() - start_time < timeout_seconds:
        try:
            running_status_result = nrc.get_robot_running_state(socket_fd, 0)
            
            if isinstance(running_status_result, list) and len(running_status_result) > 1:
                running_status = running_status_result[1]
                # 状态码：0=停止, 1=暂停, 2=运动
                if running_status == 0:
                    print(" ✅")
                    return True
            else:
                 print(f"\n  (获取运行状态返回值异常: {running_status_result})")
                 time.sleep(1)
                 continue

            if time.time() - last_print_time > 3:
                print(".", end="", flush=True)
                last_print_time = time.time()
            time.sleep(0.5)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(1)

    print(" ❌ 队列执行超时!")
    return False


def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status_result = nrc.get_servo_state(socket_fd, 0)
        servo_status = -1
        if isinstance(servo_status_result, list) and len(servo_status_result) > 1:
            servo_status = servo_status_result[1]
        
        if servo_status in [1, 3]: # 1-就绪, 3-运行
            print("✅ 机器人伺服已上电就绪。")
            return True

        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(1)
        
        servo_status_result = nrc.get_servo_state(socket_fd, 0)
        if isinstance(servo_status_result, list) and len(servo_status_result) > 1:
            servo_status = servo_status_result[1]
        
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1) # 切换到伺服就绪
            time.sleep(1)

        servo_status_result = nrc.get_servo_state(socket_fd, 0)
        if isinstance(servo_status_result, list) and len(servo_status_result) > 1 and servo_status_result[1] in [1, 3]:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {servo_status_result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False


def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False


def create_move_command(point):
    """根据G-code点创建移动命令"""
    gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
    gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
    gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

    rx_deg = normalize_angle_degrees(gcode_a + GCODE_TO_ROBOT_OFFSET_A)
    ry_deg = normalize_angle_degrees(gcode_b + GCODE_TO_ROBOT_OFFSET_B)
    rz_deg = normalize_angle_degrees(gcode_c + GCODE_TO_ROBOT_OFFSET_C)

    rx_rad = math.radians(rx_deg)
    ry_rad = math.radians(ry_deg)
    rz_rad = math.radians(rz_deg)

    pos = nrc.VectorDouble()
    target_pos = [point['x'], point['y'], point['z'], rx_rad, ry_rad, rz_rad]
    for val in target_pos:
        pos.append(val)

    cmd = nrc.MoveCmd()
    
    # --- 核心修复 ---
    # 当 targetPosValue 包含实际坐标数据时, targetPosType 应为 PosType_data (值为0)
    cmd.targetPosType = nrc.PosType_data  # 或者直接写 0
    cmd.targetPosValue = pos
    
    # 坐标系类型: 0=关节, 1=笛卡尔(世界), 2=工具, 3=用户
    # 因为我们提供了笛卡尔坐标，并希望在指定的用户坐标系下运动，
    # 所以 coord 应该设置为 3，并指定 userNum。
    cmd.coord = 3
    cmd.userNum = USER_COORD_NUMBER
    # --- 修复结束 ---
    
    cmd.velocity = VELOCITY_PERCENT
    cmd.acc = ACCEL_PERCENT
    cmd.dec = ACCEL_PERCENT
    cmd.pl = SMOOTHING_LEVEL

    return cmd, (rx_deg, ry_deg, rz_deg)


def execute_gcode_with_queue():
    """使用队列模式执行G-code的主函数"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序 (队列模式)")
    print("=" * 60)

    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        if not robot_power_on_if_needed(socket_fd):
            return

        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        print(f"ℹ️ 队列模式配置: 批次大小={QUEUE_BATCH_SIZE}, 速度={VELOCITY_PERCENT}%, 加速度={ACCEL_PERCENT}%, 平滑度={SMOOTHING_LEVEL}")
        print(f"ℹ️ 机械臂默认姿态: RX={DEFAULT_RX:.1f}°, RY={DEFAULT_RY:.1f}°, RZ={DEFAULT_RZ:.1f}°")

        # 先切换到运行模式设置速度
        result = nrc.set_current_mode(socket_fd, 2)  # 2 = 运行模式
        if result != 0:
            print(f"❌ 设置运行模式失败，错误码: {result}")
            return False
        print("✅ 运行模式设置成功")
        time.sleep(0.5)

        # 设置运行模式下的全局速度为100%（这将影响队列模式的实际执行速度）
        result = nrc.set_speed(socket_fd, 50)  # 100% 全局速度
        if result != 0:
            print(f"❌ 设置运行模式全局速度失败，错误码: {result}")
            return False
        print("✅ 运行模式全局速度设置为100%")
        time.sleep(0.5)
        print("\n🔄 启用队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启用队列模式失败，错误码: {result}")
            return
        print("✅ 队列模式已启用")

        total_points = len(gcode_path)
        batch_count = (total_points + QUEUE_BATCH_SIZE - 1) // QUEUE_BATCH_SIZE
        
        print(f"\n🚀 开始执行 {total_points} 个路径点，分为 {batch_count} 个批次...")
        print("=" * 40)

        for batch_idx in range(batch_count):
            start_idx = batch_idx * QUEUE_BATCH_SIZE
            end_idx = min(start_idx + QUEUE_BATCH_SIZE, total_points)
            batch_points = gcode_path[start_idx:end_idx]
            
            print(f"\n--- 批次 {batch_idx + 1}/{batch_count}: 点 {start_idx + 1}-{end_idx} ---")
            
            # 使用 queue_motion_clear_Data 
            if hasattr(nrc, 'queue_motion_clear_Data'):
                nrc.queue_motion_clear_Data(socket_fd)

            queue_size = 0
            for i, point in enumerate(batch_points):
                global_idx = start_idx + i
                cmd, predicted_angles = create_move_command(point)
                
                result = nrc.queue_motion_push_back_moveL(socket_fd, cmd)
                if result == 0:
                    queue_size += 1
                    print(f"  📍 已添加点 {global_idx + 1}/{total_points} 到本地队列")
                else:
                    print(f"❌ 点 {global_idx + 1} 添加到队列失败，错误码: {result}")
                    raise Exception(f"添加队列失败，错误码 {result}")
            
            if queue_size == 0:
                print("⚠️ 当前批次没有成功添加任何指令到队列，跳过。")
                continue
            
            print(f"  📤 正在发送 {queue_size} 条指令到控制器...")
            result = nrc.queue_motion_send_to_controller(socket_fd, queue_size)
            if result != 0:
                print(f"❌ 发送队列到控制器失败，错误码: {result}")
                raise Exception(f"发送队列失败，错误码 {result}")
            
            if not wait_for_queue_complete(socket_fd, QUEUE_TIMEOUT):
                print(f"❌ 批次 {batch_idx + 1} 执行超时")
                raise Exception("队列执行超时")
            
            print(f"  ✅ 批次 {batch_idx + 1} 执行完成")
            
            if batch_idx < batch_count - 1:
                time.sleep(0.5)

        print("\n" + "=" * 40)
        print("🎉 所有G-code路径执行完毕！")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
        print("   正在尝试安全停止并下电...")
    finally:
        if socket_fd > 0:
            try:
                print("\n🔄 停止运动并关闭队列模式...")
                if hasattr(nrc, 'queue_motion_stop'):
                    nrc.queue_motion_stop(socket_fd) # 使用正确的停止函数
                    time.sleep(0.5)
                nrc.queue_motion_set_status(socket_fd, False)
                time.sleep(0.5)
                    # 设置回示教模式
                result = nrc.set_current_mode(socket_fd, 0)  # 0 = 示教模式
                if result != 0:
                    print(f"⚠️ 设置示教模式失败，错误码: {result}")
                else:
                    print("✅ 已切换回示教模式")
            except Exception as e_finally:
                print(f"⚠️ 在最终清理步骤中发生错误: {e_finally}")
            
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_with_queue()