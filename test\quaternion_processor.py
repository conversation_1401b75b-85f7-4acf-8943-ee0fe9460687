#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四元数处理核心模块
实现欧拉角到四元数转换、球面线性插值(SLERP)、奇异性规避等核心功能
"""

import numpy as np
import math
from typing import List, Tuple, Optional


class QuaternionProcessor:
    """四元数处理器 - 解决机械臂3D打印中的姿态控制问题"""
    
    def __init__(self):
        self.last_quat = None
        self.singularity_threshold = 0.1  # 奇异性检测阈值
        
    def euler_to_quaternion(self, a: float, b: float, c: float) -> np.ndarray:
        """
        将欧拉角(度)转换为四元数
        机械臂通常使用ZYX旋转顺序 (Yaw-Pitch-Roll)
        
        Args:
            a, b, c: 欧拉角(度) - 对应RX, RY, RZ
            
        Returns:
            quaternion: [x, y, z, w] 四元数
        """
        # 转换为弧度
        roll = math.radians(a)   # RX
        pitch = math.radians(b)  # RY  
        yaw = math.radians(c)    # RZ
        
        # ZYX旋转顺序的四元数计算
        cy = math.cos(yaw * 0.5)
        sy = math.sin(yaw * 0.5)
        cp = math.cos(pitch * 0.5)
        sp = math.sin(pitch * 0.5)
        cr = math.cos(roll * 0.5)
        sr = math.sin(roll * 0.5)
        
        w = cr * cp * cy + sr * sp * sy
        x = sr * cp * cy - cr * sp * sy
        y = cr * sp * cy + sr * cp * sy
        z = cr * cp * sy - sr * sp * cy
        
        return np.array([x, y, z, w])
    
    def quaternion_to_euler(self, quat: np.ndarray) -> Tuple[float, float, float]:
        """
        将四元数转换为欧拉角(度)
        
        Args:
            quat: [x, y, z, w] 四元数
            
        Returns:
            (a, b, c): 欧拉角(度) - 对应RX, RY, RZ
        """
        x, y, z, w = quat
        
        # Roll (RX)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = math.atan2(sinr_cosp, cosr_cosp)
        
        # Pitch (RY)
        sinp = 2 * (w * y - z * x)
        if abs(sinp) >= 1:
            pitch = math.copysign(math.pi / 2, sinp)  # 使用90度，避免奇异性
        else:
            pitch = math.asin(sinp)
        
        # Yaw (RZ)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
        
        # 转换为度
        return (math.degrees(roll), math.degrees(pitch), math.degrees(yaw))
    
    def quaternion_slerp(self, q1: np.ndarray, q2: np.ndarray, t: float) -> np.ndarray:
        """
        四元数球面线性插值 (SLERP)
        
        Args:
            q1, q2: 起始和结束四元数 [x, y, z, w]
            t: 插值参数 [0, 1]
            
        Returns:
            interpolated_quat: 插值后的四元数
        """
        # 确保四元数归一化
        q1 = q1 / np.linalg.norm(q1)
        q2 = q2 / np.linalg.norm(q2)
        
        # 计算点积
        dot = np.dot(q1, q2)
        
        # 如果点积为负，取反其中一个四元数以选择较短路径
        if dot < 0.0:
            q2 = -q2
            dot = -dot
        
        # 如果四元数非常接近，使用线性插值
        if dot > 0.9995:
            result = q1 + t * (q2 - q1)
            return result / np.linalg.norm(result)
        
        # 计算插值角度
        theta_0 = math.acos(abs(dot))
        sin_theta_0 = math.sin(theta_0)
        
        theta = theta_0 * t
        sin_theta = math.sin(theta)
        
        s0 = math.cos(theta) - dot * sin_theta / sin_theta_0
        s1 = sin_theta / sin_theta_0
        
        return s0 * q1 + s1 * q2
    
    def interpolate_orientation(self, start_euler: Tuple[float, float, float], 
                              end_euler: Tuple[float, float, float], 
                              steps: int = 5) -> List[Tuple[float, float, float]]:
        """
        四元数姿态插值 - 生成平滑的姿态过渡序列
        
        Args:
            start_euler, end_euler: 起始和结束欧拉角(度)
            steps: 插值步数
            
        Returns:
            interpolated_orientations: 插值后的欧拉角序列
        """
        # 转换为四元数
        q_start = self.euler_to_quaternion(*start_euler)
        q_end = self.euler_to_quaternion(*end_euler)
        
        # 生成插值序列
        orientations = []
        for i in range(1, steps + 1):
            t = i / steps
            q_interp = self.quaternion_slerp(q_start, q_end, t)
            euler_interp = self.quaternion_to_euler(q_interp)
            orientations.append(euler_interp)
        
        return orientations
    
    def detect_singularity(self, euler_angles: Tuple[float, float, float]) -> bool:
        """
        检测欧拉角奇异性 (万向节锁)
        
        Args:
            euler_angles: (a, b, c) 欧拉角(度)
            
        Returns:
            is_singular: 是否接近奇异点
        """
        _, b, _ = euler_angles
        # 当B角(pitch)接近±90°时发生万向节锁
        return abs(abs(b) - 90) < 5.0  # 5度容差
    
    def singularity_avoidance(self, euler_angles: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """
        奇异性规避策略
        
        Args:
            euler_angles: 原始欧拉角(度)
            
        Returns:
            corrected_angles: 修正后的欧拉角(度)
        """
        a, b, c = euler_angles
        
        # 检测万向节锁
        if abs(abs(b) - 90) < 5.0:
            print(f"⚠️ 检测到万向节锁风险: B={b:.1f}°, 应用规避策略")
            
            # 策略1: 使用等效旋转表示
            if b > 85:
                # B接近+90°
                new_a = a + 180
                new_b = 180 - b
                new_c = c + 180
            elif b < -85:
                # B接近-90°
                new_a = a + 180
                new_b = -180 - b
                new_c = c + 180
            else:
                return euler_angles
            
            # 角度规范化
            corrected = self.normalize_angles((new_a, new_b, new_c))
            print(f"✅ 规避后角度: A={corrected[0]:.1f}°, B={corrected[1]:.1f}°, C={corrected[2]:.1f}°")
            return corrected
        
        return euler_angles
    
    def normalize_angles(self, angles: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """
        将角度规范化到[-180, 180]范围
        
        Args:
            angles: (a, b, c) 角度(度)
            
        Returns:
            normalized_angles: 规范化后的角度
        """
        def normalize_single(angle):
            while angle > 180:
                angle -= 360
            while angle <= -180:
                angle += 360
            return angle
        
        return tuple(normalize_single(a) for a in angles)
    
    def calculate_orientation_distance(self, euler1: Tuple[float, float, float], 
                                     euler2: Tuple[float, float, float]) -> float:
        """
        计算两个姿态之间的角度距离
        
        Args:
            euler1, euler2: 两个欧拉角姿态(度)
            
        Returns:
            distance: 角度距离(度)
        """
        q1 = self.euler_to_quaternion(*euler1)
        q2 = self.euler_to_quaternion(*euler2)
        
        # 计算四元数之间的角度距离
        dot = abs(np.dot(q1, q2))
        dot = min(1.0, dot)  # 防止数值误差
        
        angle_rad = 2 * math.acos(dot)
        return math.degrees(angle_rad)
    
    def adaptive_interpolation_steps(self, start_euler: Tuple[float, float, float],
                                   end_euler: Tuple[float, float, float],
                                   max_step_angle: float = 5.0) -> int:
        """
        自适应计算插值步数
        
        Args:
            start_euler, end_euler: 起始和结束姿态
            max_step_angle: 每步最大角度变化(度)
            
        Returns:
            steps: 建议的插值步数
        """
        distance = self.calculate_orientation_distance(start_euler, end_euler)
        steps = max(1, int(math.ceil(distance / max_step_angle)))
        return min(steps, 20)  # 限制最大步数
